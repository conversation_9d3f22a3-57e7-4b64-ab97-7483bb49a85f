import pandas as pd
import numpy as np
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', 100)

df = pd.read_csv("./raw_data/20250320_gran_base_signature_v2.csv", delimiter = ";")

# Count and percentage of missing values
missing_values = pd.DataFrame({
    'Missing Count': df.isna().sum(),
    'Missing Percentage': (df.isna().sum() / len(df) * 100).round(2)
})
missing_values.head()

#imputing all columns with data type = 0
for col in df.select_dtypes(include="object").columns:
    df[col] = df[col].fillna("DESCONOCIDO")

# Convert to datetime format
df['fec_hora_ocurrencia'] = pd.to_datetime(df['fec_hora_ocurrencia'])

unique_years = df['fec_hora_ocurrencia'].dt.year.unique()
unique_months = df['fec_hora_ocurrencia'].dt.to_period('M').unique()

print(f"\nUnique years: {sorted(unique_years)}")
print(f"Number of unique years: {len(unique_years)}")

print(f"\nUnique year-months: {sorted(unique_months)}")
print(f"Number of unique months: {len(unique_months)}")

#crea nueva columna mnt_beneficio_USD que es el mismo valore en mnt_beneficio_sol en dolares
tipo_cambio = 3.75  # 1 USD = 3.7 soles peruanos
df["mnt_beneficio_USD"] = df["mnt_beneficio_sol"] / tipo_cambio
df["mnt_beneficio_USD"] = df["mnt_beneficio_USD"].round(2)

#exploración
negativos = df[df['mnt_beneficio_USD'] < 0]
print(f"Cantidad de registros con precio negativo: {len(negativos)}")
print("\nEstadísticas de los precios negativos:")
print(negativos['mnt_beneficio_USD'].describe().apply(lambda x: f"{x:,.2f}"))

print("\nEjemplos de registros con precio negativo:")
#display(negativos.head(10))

# Imputar valores negativos en mnt_beneficio_USD con su valor absoluto
df['mnt_beneficio_USD'] = df['mnt_beneficio_USD'].apply(lambda x: abs(x) if x < 0 else x)

# Verificar que ya no hay valores negativos
print(f"Registros con precio negativo después de la imputación: {(df['mnt_beneficio_USD'] < 0).sum()}")

df.clasificacion_de_riesgo.value_counts()

df.descrpicion_riesgo.value_counts()


df.especialidad_diagnostico.value_counts()

df.des_cobertura.value_counts()

df.des_tipo_reclamo.value_counts()

# 1. Análisis de Costos (mnt_beneficio_USD)
print("Estadísticas básicas para los costos de tratamiento en USD (formateadas):")
desc = df['mnt_beneficio_USD'].describe().apply(lambda x: f"{x:,.2f}")
print(desc)

# Visualizar la distribución de costos en USD
import matplotlib.pyplot as plt
import seaborn as sns

plt.figure(figsize=(12, 6))
# Graficar la distribución sin valores extremos (hasta el percentil 95)
cost_95th = df['mnt_beneficio_USD'].quantile(0.95)
sns.histplot(data=df[df['mnt_beneficio_USD'] <= cost_95th], x='mnt_beneficio_USD', bins=50)
plt.title('Distribución de Costos de Tratamiento en USD (hasta el percentil 95)')
plt.xlabel('Costo (mnt_beneficio_USD)')
plt.show()

# Imprimir algunos percentiles para considerar umbrales (formateados)
percentiles = [25, 50, 75, 90, 95, 99,100]
print("\nPercentiles de costo en USD para considerar umbrales:")
for p in percentiles:
    value = df['mnt_beneficio_USD'].quantile(p/100)
    print(f"Percentil {p}: {value:,.2f}")

# 2. Análisis de Clasificación de Riesgo
print("Distribución de la Clasificación de Riesgo:")
risk_dist = df['clasificacion_de_riesgo'].value_counts()
print(risk_dist)
print("\nDistribución porcentual:")
print((risk_dist / len(df) * 100).round(2))

# Visualizar relación entre riesgo y costo en USD
plt.figure(figsize=(12, 6))
sns.boxplot(data=df, x='clasificacion_de_riesgo', y='mnt_beneficio_USD')
plt.xticks(rotation=45)
plt.title('Distribución de Costos en USD según Clasificación de Riesgo')
plt.ylabel('Costo (mnt_beneficio_USD)')
plt.show()

# Calcular costo promedio por nivel de riesgo en USD
print("\nCosto promedio en USD por clasificación de riesgo:")
print(df.groupby('clasificacion_de_riesgo')['mnt_beneficio_USD'].mean().round(2))

df.descrpicion_riesgo.value_counts()

# 3. Análisis de Procedimientos y Coberturas
print("Top 20 procedimientos más comunes:")
print(df['des_procedimiento'].value_counts().head(20))

print("\nTop 20 tipos de cobertura:")
print(df['des_cobertura'].value_counts().head(20))

# Calcular costo promedio por tipo de cobertura en USD
coverage_cost = df.groupby('des_cobertura')['mnt_beneficio_USD'].agg(['mean', 'count']).round(2)
coverage_cost = coverage_cost.sort_values('mean', ascending=False)
print("\nCosto promedio en USD por tipo de cobertura (top 20):")
print(coverage_cost.head(20))

df.head() #des_diagnostico_salud, des_grupo_diagnostico, des_agrupacion_diagnostico

# 1. Análisis de Costos (mnt_beneficio_USD)
print("Estadísticas básicas para los costos de tratamiento en USD (formateadas):")
desc = df['mnt_beneficio_USD'].describe().apply(lambda x: f"{x:,.2f}")
print(desc)

# Visualizar la distribución de costos en USD
import matplotlib.pyplot as plt
import seaborn as sns

plt.figure(figsize=(12, 6))
# Graficar la distribución sin valores extremos (hasta el percentil 95)
cost_95th = df['mnt_beneficio_USD'].quantile(0.95)
sns.histplot(data=df[df['mnt_beneficio_USD'] <= cost_95th], x='mnt_beneficio_USD', bins=50)
plt.title('Distribución de Costos de Tratamiento en USD (hasta el percentil 95)')
plt.xlabel('Costo (mnt_beneficio_USD)')
plt.show()

# Imprimir algunos percentiles para considerar umbrales (formateados)
percentiles = [25, 50, 75, 90, 95, 99,100]
print("\nPercentiles de costo en USD para considerar umbrales:")
for p in percentiles:
    value = df['mnt_beneficio_USD'].quantile(p/100)
    print(f"Percentil {p}: {value:,.2f}")

# 1. Análisis de Costos (mnt_beneficio_USD)
print("Estadísticas básicas para los costos de tratamiento en USD (formateadas):")
desc = df['mnt_beneficio_USD'].describe().apply(lambda x: f"{x:,.2f}")
print(desc)

# Visualizar la distribución de costos en USD
import matplotlib.pyplot as plt
import seaborn as sns

plt.figure(figsize=(12, 6))
# Graficar la distribución sin valores extremos (hasta el percentil 95)
cost_95th = df['mnt_beneficio_USD'].quantile(0.95)
sns.histplot(data=df[df['mnt_beneficio_USD'] <= cost_95th], x='mnt_beneficio_USD', bins=50)
plt.title('Distribución de Costos de Tratamiento en USD (hasta el percentil 95)')
plt.xlabel('Costo (mnt_beneficio_USD)')
plt.show()

# Imprimir algunos percentiles para considerar umbrales (formateados)
percentiles = [25, 50, 75, 90, 95, 99,100]
print("\nPercentiles de costo en USD para considerar umbrales:")
for p in percentiles:
    value = df['mnt_beneficio_USD'].quantile(p/100)
    print(f"Percentil {p}: {value:,.2f}")

# 1. Análisis de Costos (mnt_beneficio_USD)
print("Estadísticas básicas para los costos de tratamiento en USD (formateadas):")
desc = df['mnt_beneficio_USD'].describe().apply(lambda x: f"{x:,.2f}")
print(desc)

# Visualizar la distribución de costos en USD
import matplotlib.pyplot as plt
import seaborn as sns

plt.figure(figsize=(12, 6))
# Graficar la distribución sin valores extremos (hasta el percentil 95)
cost_95th = df['mnt_beneficio_USD'].quantile(0.95)
sns.histplot(data=df[df['mnt_beneficio_USD'] <= cost_95th], x='mnt_beneficio_USD', bins=50)
plt.title('Distribución de Costos de Tratamiento en USD (hasta el percentil 95)')
plt.xlabel('Costo (mnt_beneficio_USD)')
plt.show()

# Imprimir algunos percentiles para considerar umbrales (formateados)
percentiles = [25, 50, 75, 90, 95, 99,100]
print("\nPercentiles de costo en USD para considerar umbrales:")
for p in percentiles:
    value = df['mnt_beneficio_USD'].quantile(p/100)
    print(f"Percentil {p}: {value:,.2f}")

# 1. Análisis de Costos (mnt_beneficio_USD)
print("Estadísticas básicas para los costos de tratamiento en USD (formateadas):")
desc = df['mnt_beneficio_USD'].describe().apply(lambda x: f"{x:,.2f}")
print(desc)

# Visualizar la distribución de costos en USD
import matplotlib.pyplot as plt
import seaborn as sns

plt.figure(figsize=(12, 6))
# Graficar la distribución sin valores extremos (hasta el percentil 95)
cost_95th = df['mnt_beneficio_USD'].quantile(0.95)
sns.histplot(data=df[df['mnt_beneficio_USD'] <= cost_95th], x='mnt_beneficio_USD', bins=50)
plt.title('Distribución de Costos de Tratamiento en USD (hasta el percentil 95)')
plt.xlabel('Costo (mnt_beneficio_USD)')
plt.show()

# Imprimir algunos percentiles para considerar umbrales (formateados)
percentiles = [25, 50, 75, 90, 95, 99,100]
print("\nPercentiles de costo en USD para considerar umbrales:")
for p in percentiles:
    value = df['mnt_beneficio_USD'].quantile(p/100)
    print(f"Percentil {p}: {value:,.2f}")

# Calcula los percentiles una sola vez
p25 = df['mnt_beneficio_USD'].quantile(0.25)
p75 = df['mnt_beneficio_USD'].quantile(0.75)

def complejidad_por_costo(costo, p25, p75):
    """Clasifica la complejidad según percentiles de costo en USD"""
    if pd.isna(costo):
        return 'Desconocido'
    elif costo <= p25:
        return 'Baja'
    elif costo <= p75:
        return 'Media'
    else:
        return 'Alta'

df['complejidad_costo'] = df['mnt_beneficio_USD'].apply(lambda x: complejidad_por_costo(x, p25, p75))

def complejidad_por_riesgo(riesgo):
    """Clasifica la complejidad según valores personalizados de clasificacion_de_riesgo"""
    if riesgo in ["G2", "G3", "G0"]:
        return "Baja"
    # Puedes cambiar "Media" por "Alta" si lo prefieres
    elif pd.isna(riesgo) or riesgo == "DESCONOCIDO":
        return "Desconocido"
    else:
        return "Media"

df['complejidad_riesgo'] = df['clasificacion_de_riesgo'].apply(complejidad_por_riesgo)

# Define tus listas de procedimientos por nivel de complejidad
procedimientos_baja = ["AMBULATORIO"]
procedimientos_media = []  # Agrega aquí si tienes
procedimientos_alta = []   # Agrega aquí si tienes

def complejidad_por_procedimiento(procedimiento):
    if procedimiento in procedimientos_baja:
        return "Baja"
    elif procedimiento in procedimientos_media:
        return "Media"
    elif procedimiento in procedimientos_alta:
        return "Alta"
    elif pd.isna(procedimiento) or procedimiento == "DESCONOCIDO":
        return "Desconocido"
    else:
        return "Alta"  # Por defecto, puedes cambiarlo a "Alta" si prefieres

df['complejidad_procedimiento'] = df['des_procedimiento'].apply(complejidad_por_procedimiento)

# Matriz de confusión 3D: costo, riesgo y procedimiento

# Concatenar las tres variables en una sola columna
df['combinacion_complejidad'] = (
    df['complejidad_costo'] + '-' +
    df['complejidad_riesgo'] + '-' +
    df['complejidad_procedimiento']
)

# Ver la tabla de frecuencias de todas las combinaciones
matriz_3d = df['combinacion_complejidad'].value_counts().to_frame('conteo')
matriz_3d['porcentaje'] = (matriz_3d['conteo'] / len(df) * 100).round(2)

print("Matriz de confusión para las tres variables (conteo y porcentaje):")
display(matriz_3d)

df.id_persona_afiliado.value_counts()

# Paso 1: Calcula los valores agregados solo una vez por episodio
episodio_aggs = df.groupby('episodio_id').agg(
    id_persona_afiliado=('id_persona_afiliado', 'first'),
    fecha_ocurrencia=('fecha_ocurrencia', 'first'),
    dias_consecutivos=('fecha_ocurrencia', 'nunique'),
    combinacion_complejidad_concat=('combinacion_complejidad', lambda x: '|'.join(x.astype(str))),
    complejidad_hibrida_concat=('complejidad_hibrida', lambda x: '|'.join(x.astype(str)))
).reset_index()

# Paso 2: Si quieres añadir estos resultados al dataframe original:
df = df.merge(
    episodio_aggs[['episodio_id', 'dias_consecutivos', 'combinacion_complejidad_concat', 'complejidad_hibrida_concat']],
    on='episodio_id',
    how='left'
)

# Paso 3: Si solo quieres el dataframe consolidado por episodio:
df_episodios = episodio_aggs

# Opcional: muestra el resultado
df_episodios.head()

df.id_persona_afiliado.value_counts().head(200)

